import React, { useContext } from "react";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  CardContent,
  FormControlLabel,
  Switch,
  TextField,
  Button,
  Stack,
  Chip,
  Rating,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  OutlinedInput,
  Checkbox,
  ListItemText,
  Alert,
  Divider,
} from "@mui/material";
import { TimePicker } from "@mui/x-date-pickers/TimePicker";
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import dayjs, { Dayjs } from "dayjs";
import { Formik, Form } from "formik";
import * as yup from "yup";
import { useDispatch } from "react-redux";
import { LoadingContext } from "../../../../context/loading.context";
import { ToastContext } from "../../../../context/toast.context";
import { ToastSeverity } from "../../../../constants/toastSeverity.constant";
import ReviewSettingsService, {
  IAutoReplySettings,
} from "../../../../services/reviewSettings/reviewSettings.service";
import SaveOutlinedIcon from "@mui/icons-material/SaveOutlined";

interface IAutoReplySettingsProps {
  businessId: number;
  settings: IAutoReplySettings | null;
  onSettingsUpdate: () => void;
}

const validationSchema = yup.object({
  isEnabled: yup.boolean(),
  enabledStarRatings: yup.array().of(yup.number()),
  delayMinutes: yup
    .number()
    .min(0, "Delay must be 0 or greater")
    .max(1440, "Delay cannot exceed 24 hours (1440 minutes)"),
  onlyBusinessHours: yup.boolean(),
  businessHoursStart: yup.string(),
  businessHoursEnd: yup.string(),
});

const STAR_RATINGS = [1, 2, 3, 4, 5];

const AutoReplySettingsComponent: React.FunctionComponent<
  IAutoReplySettingsProps
> = ({ businessId, settings, onSettingsUpdate }) => {
  const dispatch = useDispatch();
  const { setLoading } = useContext(LoadingContext);
  const { setToastConfig } = useContext(ToastContext);

  const _reviewSettingsService = new ReviewSettingsService(dispatch);

  const initialValues = {
    isEnabled: settings?.is_enabled || false,
    enabledStarRatings: settings?.enabled_star_ratings || [],
    delayMinutes: settings?.delay_minutes || 0,
    onlyBusinessHours: settings?.only_business_hours || false,
    businessHoursStart: settings?.business_hours_start || "09:00:00",
    businessHoursEnd: settings?.business_hours_end || "17:00:00",
  };

  const handleSubmit = async (values: any) => {
    try {
      setLoading(true);

      const settingsData = {
        is_enabled: values.isEnabled,
        enabled_star_ratings: values.enabledStarRatings,
        delay_minutes: values.delayMinutes,
        only_business_hours: values.onlyBusinessHours,
        business_hours_start: values.businessHoursStart,
        business_hours_end: values.businessHoursEnd,
      };

      await _reviewSettingsService.updateAutoReplySettings(
        businessId,
        settingsData
      );

      setToastConfig(
        ToastSeverity.Success,
        "Auto-reply settings updated successfully",
        true
      );
      onSettingsUpdate();
    } catch (error) {
      setToastConfig(
        ToastSeverity.Error,
        "Failed to update auto-reply settings",
        true
      );
    } finally {
      setLoading(false);
    }
  };

  const formatTime = (timeString: string) => {
    return dayjs(`2000-01-01 ${timeString}`).format("h:mm A");
  };

  return (
    <LocalizationProvider dateAdapter={AdapterDayjs}>
      <Box>
        <Typography variant="h6" gutterBottom>
          Auto-Reply Settings
        </Typography>
        <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
          Configure automatic replies to reviews based on star ratings and
          business hours.
        </Typography>

        <Formik
          initialValues={initialValues}
          validationSchema={validationSchema}
          onSubmit={handleSubmit}
          enableReinitialize
        >
          {({ values, errors, touched, handleChange, setFieldValue }) => (
            <Form>
              <Stack spacing={3}>
                <Card variant="outlined">
                  <CardContent>
                    <FormControlLabel
                      control={
                        <Switch
                          checked={values.isEnabled}
                          onChange={(e) =>
                            setFieldValue("isEnabled", e.target.checked)
                          }
                          name="isEnabled"
                        />
                      }
                      label="Enable Auto-Reply"
                    />
                    <Typography
                      variant="body2"
                      color="text.secondary"
                      sx={{ mt: 1 }}
                    >
                      Automatically send replies to new reviews based on your
                      templates
                    </Typography>
                  </CardContent>
                </Card>

                {values.isEnabled && (
                  <>
                    <Card variant="outlined">
                      <CardContent>
                        <Typography variant="subtitle1" gutterBottom>
                          Star Ratings to Auto-Reply
                        </Typography>
                        <Typography
                          variant="body2"
                          color="text.secondary"
                          sx={{ mb: 2 }}
                        >
                          Select which star ratings should trigger automatic
                          replies
                        </Typography>

                        <FormControl fullWidth>
                          <InputLabel>Enabled Star Ratings</InputLabel>
                          <Select
                            multiple
                            value={values.enabledStarRatings}
                            onChange={(e) =>
                              setFieldValue(
                                "enabledStarRatings",
                                e.target.value
                              )
                            }
                            input={
                              <OutlinedInput label="Enabled Star Ratings" />
                            }
                            renderValue={(selected) => (
                              <Box
                                sx={{
                                  display: "flex",
                                  flexWrap: "wrap",
                                  gap: 0.5,
                                }}
                              >
                                {(selected as number[]).map((value) => (
                                  <Chip
                                    key={value}
                                    label={
                                      <Box
                                        sx={{
                                          display: "flex",
                                          alignItems: "center",
                                        }}
                                      >
                                        <Rating
                                          value={value}
                                          readOnly
                                          size="small"
                                          sx={{ mr: 0.5 }}
                                        />
                                        {value}
                                      </Box>
                                    }
                                    size="small"
                                  />
                                ))}
                              </Box>
                            )}
                          >
                            {STAR_RATINGS.map((rating) => (
                              <MenuItem key={rating} value={rating}>
                                <Checkbox
                                  checked={
                                    values.enabledStarRatings.indexOf(rating) >
                                    -1
                                  }
                                />
                                <ListItemText
                                  primary={
                                    <Box
                                      sx={{
                                        display: "flex",
                                        alignItems: "center",
                                      }}
                                    >
                                      <Rating
                                        value={rating}
                                        readOnly
                                        size="small"
                                        sx={{ mr: 1 }}
                                      />
                                      {rating} Star{rating !== 1 ? "s" : ""}
                                    </Box>
                                  }
                                />
                              </MenuItem>
                            ))}
                          </Select>
                        </FormControl>

                        {values.enabledStarRatings.length === 0 && (
                          <Alert severity="warning" sx={{ mt: 2 }}>
                            Please select at least one star rating to enable
                            auto-reply functionality.
                          </Alert>
                        )}
                      </CardContent>
                    </Card>

                    <Card variant="outlined">
                      <CardContent>
                        <Typography variant="subtitle1" gutterBottom>
                          Reply Timing
                        </Typography>

                        <TextField
                          fullWidth
                          type="number"
                          name="delayMinutes"
                          label="Delay (minutes)"
                          value={values.delayMinutes}
                          onChange={handleChange}
                          error={
                            touched.delayMinutes && Boolean(errors.delayMinutes)
                          }
                          helperText={
                            (touched.delayMinutes &&
                            typeof errors.delayMinutes === "string"
                              ? errors.delayMinutes
                              : null) ||
                            "How long to wait before sending auto-reply (0 = immediate)"
                          }
                          inputProps={{ min: 0, max: 1440 }}
                        />
                      </CardContent>
                    </Card>

                    <Card variant="outlined">
                      <CardContent>
                        <FormControlLabel
                          control={
                            <Switch
                              checked={values.onlyBusinessHours}
                              onChange={(e) =>
                                setFieldValue(
                                  "onlyBusinessHours",
                                  e.target.checked
                                )
                              }
                              name="onlyBusinessHours"
                            />
                          }
                          label="Only during business hours"
                        />
                        <Typography
                          variant="body2"
                          color="text.secondary"
                          sx={{ mb: 2 }}
                        >
                          Only send auto-replies during specified business hours
                        </Typography>

                        {values.onlyBusinessHours && (
                          <Stack direction="row" spacing={2} sx={{ mt: 2 }}>
                            <TimePicker
                              label="Business Hours Start"
                              value={dayjs(
                                `2000-01-01 ${values.businessHoursStart}`
                              )}
                              onChange={(newValue: Dayjs | null) => {
                                if (newValue) {
                                  setFieldValue(
                                    "businessHoursStart",
                                    newValue.format("HH:mm:ss")
                                  );
                                }
                              }}
                              slotProps={{
                                textField: {
                                  fullWidth: true,
                                },
                              }}
                            />
                            <TimePicker
                              label="Business Hours End"
                              value={dayjs(
                                `2000-01-01 ${values.businessHoursEnd}`
                              )}
                              onChange={(newValue: Dayjs | null) => {
                                if (newValue) {
                                  setFieldValue(
                                    "businessHoursEnd",
                                    newValue.format("HH:mm:ss")
                                  );
                                }
                              }}
                              slotProps={{
                                textField: {
                                  fullWidth: true,
                                },
                              }}
                            />
                          </Stack>
                        )}
                      </CardContent>
                    </Card>

                    {values.isEnabled &&
                      values.enabledStarRatings.length > 0 && (
                        <Alert severity="info">
                          <Typography variant="body2">
                            <strong>Auto-Reply Summary:</strong>
                            <br />• Enabled for{" "}
                            {values.enabledStarRatings.length} star rating(s):{" "}
                            {values.enabledStarRatings.join(", ")}
                            <br />• Delay:{" "}
                            {values.delayMinutes === 0
                              ? "Immediate"
                              : `${values.delayMinutes} minutes`}
                            <br />• Business hours:{" "}
                            {values.onlyBusinessHours
                              ? `${formatTime(
                                  values.businessHoursStart
                                )} - ${formatTime(values.businessHoursEnd)}`
                              : "24/7"}
                          </Typography>
                        </Alert>
                      )}
                  </>
                )}

                <Divider />

                <Box sx={{ display: "flex", justifyContent: "flex-end" }}>
                  <Button
                    type="submit"
                    variant="contained"
                    className="tableActionBtn"
                    sx={{
                      minHeight: "50px",
                    }}
                    startIcon={<SaveOutlinedIcon />}
                  >
                    <span className="responsiveHide">Save Settings</span>
                  </Button>
                </Box>
              </Stack>
            </Form>
          )}
        </Formik>
      </Box>
    </LocalizationProvider>
  );
};

export default AutoReplySettingsComponent;
