import React, { useState, useEffect } from "react";
import {
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Card,
  CardContent,
  Divider,
  IconButton,
  Dialog,
  TextField,
  DialogTitle,
  DialogContent,
  FormControl,
  Select,
  MenuItem,
  FormHelperText,
  Grid,
  InputAdornment,
  Checkbox,
  FormControlLabel,
  Chip,
} from "@mui/material";
import AddBusinessCategoryModal from "./components/addBusinessCategory.component";
import ServicesDisplay from "./components/servicesDisplay.component";
import AddIcon from "@mui/icons-material/Add";
import EditBusinessNameModal from "../../components/editBusinessName/editBusinessName.component";
import CloseIcon from "@mui/icons-material/Close";
import EditIcon from "@mui/icons-material/Edit";
import { Formik, Form, FormikProps } from "formik";
import * as yup from "yup";
import ClearIcon from "@mui/icons-material/Clear";
import { Accessibility, Flag as FlagIcon } from "@mui/icons-material";
import ChatIcon from "@mui/icons-material/Chat";
import TextsmsIcon from "@mui/icons-material/Textsms";
import LanguageIcon from "@mui/icons-material/Language";
import LocationOnIcon from "@mui/icons-material/LocationOn";
import InfoOutlinedIcon from "@mui/icons-material/InfoOutlined";
import BusinessHours from "./components/BusinessHours";
// Add this import at the top of your file
import AddSpecialHours from "./components/AddSpecialHours";
import FromTheBusiness from "../moreActivity/FromTheBusiness";
import MoreAccessibility from "../moreActivity/MoreAccessibility";
import Amenities from "../moreActivity/Amenities";
import AddChildren from "../moreActivity/AddChildren";
import Crowd from "../moreActivity/CrowdComponent";
import Parking from "../moreActivity/ParkingComponent";
import Payments from "../moreActivity/PaymentsComponent";
import Planning from "../moreActivity/PlanningComponent";
import ServiceOptions from "../moreActivity/ServiceOptions";


interface DemoScreenProps {
  title?: string;
}

const DemoScreen: React.FC<DemoScreenProps> = ({ title }) => {
  useEffect(() => {
    if (title) {
      document.title = title;
    }
  }, [title]);

  const [isAddCategoryModalOpen, setIsAddCategoryModalOpen] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isCategoriesModalOpen, setIsCategoriesModalOpen] = useState(false);
  const [isDescriptionModalOpen, setIsDescriptionModalOpen] = useState(false);
  const [isOpeningDateModalOpen, setIsOpeningDateModalOpen] = useState(false);
  const [isPhoneNumberModalOpen, setIsPhoneNumberModalOpen] = useState(false);
  const [isChatModalOpen, setIsChatModalOpen] = useState(false);
  const [chatSettings, setChatSettings] = useState<{
    chatType: string;
    phoneNumber: string;
  }>({
    chatType: "Text message",
    phoneNumber: "",
  });
  const [businessName, setBusinessName] = useState<string>(
    "Sri Eye Care Hospital - Best Ophthalmologist in RT Nagar | Best Cataract Surgeon in Bangalore"
  );
  const [businessDescription, setBusinessDescription] = useState<string>(
    "Sri Eye Care Hospital, an eye speciality hospital in RT Nagar, Bangalore, offering eye healthcare services to people for more than 25 years. We are known for the \"Patient-Centric Ecosystem\" and the medication practices benefit the patient to get quicker recovery with successful results. Our ophthalmologists specialise in providing eye treatment for Cataracts, Lasik, Retina, Glaucoma, Occuloplasty, Paediatric, and Keratoconus.\n\nWe strive to ensure that each one of you feels at home with our quality eye care services in Bangalore. And with state-of-the-art equipment, we are here to redefine the future of eye care. Sri Eye Care, an eye hospital in Bangalore is incorporated to offer world-class treatment to the people in and around the city."
  );
  const [openingDate, setOpeningDate] = useState<{
    year: string;
    month: string;
    day: string;
  }>({
    year: "1994",
    month: "April",
    day: "None",
  });
  const [categories, setCategories] = useState([
    { name: "Eye Care Clinic", isPrimary: true },
    { name: "Lasik surgeon", isPrimary: false },
    { name: "Ophthalmologist", isPrimary: false },
    { name: "Ophthalmology Clinic", isPrimary: false },
    { name: "Paediatric Ophthalmologist", isPrimary: false },
  ]);
  const [phoneNumbers, setPhoneNumbers] = useState<{
    primaryPhone: string;
    additionalPhones: string[];
  }>({
    primaryPhone: "080 6821 2859",
    additionalPhones: ["089048 33434"],
  });
  const [isWebsiteModalOpen, setIsWebsiteModalOpen] = useState(false);
  const [websiteUrl, setWebsiteUrl] = useState("https://www.sriyeyecarehospital.com/");
  const [isBusinessLocationModalOpen, setIsBusinessLocationModalOpen] = useState(false);
  const [businessLocationData, setBusinessLocationData] = useState({
    showAddress: true,
    country: "India",
    streetAddress: "9, KHM Block, RT nagar main road",
    streetAddressLine2: "Ganganagar",
    additionalAddressLines: [],
    city: "Bangalore",
    pincode: "560032",
    state: "Karnataka",
    latitude: 13.0234,
    longitude: 77.5938
  });

  // Business Name Modal
  const handleOpenBusinessNameModal = () => {
    setIsModalOpen(true);
  };

  const handleCloseBusniessNameModal = () => {
    setIsModalOpen(false);
  };

  const handleOpenAddCategoryModal = () => {
    setIsAddCategoryModalOpen(true);
  };

  const handleCloseAddCategoryModal = () => {
    setIsAddCategoryModalOpen(false);
  };

  const handleAddCategory = (categoryName: string) => {
    console.log("Added category:", categoryName);
    setIsAddCategoryModalOpen(false);
  };

  // Business Categories Modal
  const handleOpenCategoriesModal = () => {
    setIsCategoriesModalOpen(true);
  };

  const handleCloseCategoriesModal = () => {
    setIsCategoriesModalOpen(false);
  };

  // Business Description Modal
  const handleOpenDescriptionModal = () => {
    setIsDescriptionModalOpen(true);
  };

  const handleCloseDescriptionModal = () => {
    setIsDescriptionModalOpen(false);
  };

  // Business Opening Date Modal
  const handleOpenOpeningDateModal = () => {
    setIsOpeningDateModalOpen(true);
  };

  const handleCloseOpeningDateModal = () => {
    setIsOpeningDateModalOpen(false);
  };

  const handleSaveOpeningDate = (values: { year: string; month: string; day: string }) => {
    setOpeningDate(values);
    console.log("Opening date saved:", values);
    handleCloseOpeningDateModal();
  };

  const handleDeleteOpeningDate = () => {
    setOpeningDate({
      year: "",
      month: "",
      day: "None",
    });
    console.log("Opening date deleted");
    handleCloseOpeningDateModal();
  };

  // Months array for the dropdown
  const months = [
    "January", "February", "March", "April", "May", "June",
    "July", "August", "September", "October", "November", "December"
  ];

  // Days array for the dropdown
  const days = Array.from({ length: 31 }, (_, i) => (i + 1).toString());

  // Validation schema for opening date
  const openingDateValidationSchema = yup.object({
    year: yup
      .string()
      .required("Year is required")
      .matches(/^\d{4}$/, "Year must be a 4-digit number")
      .test(
        "valid-year",
        "Year must be between 1900 and current year",
        (value) => {
          const currentYear = new Date().getFullYear();
          const yearNum = parseInt(value || "0");
          return yearNum >= 1900 && yearNum <= currentYear;
        }
      ),
    month: yup.string().required("Month is required"),
    day: yup.string(),
  });

  const handleOpenPhoneNumberModal = () => {
    setIsPhoneNumberModalOpen(true);
  };

  const handleClosePhoneNumberModal = () => {
    setIsPhoneNumberModalOpen(false);
  };

  const handleSavePhoneNumbers = (values: {
    primaryPhone: string;
    additionalPhones: string[];
  }) => {
    setPhoneNumbers(values);
    console.log("Phone numbers saved:", values);
    handleClosePhoneNumberModal();
  };

  const phoneNumberValidationSchema = yup.object({
    primaryPhone: yup
      .string()
      .required("Primary phone number is required")
      .matches(
        /^[0-9\s]+$/,
        "Phone number should only contain digits and spaces"
      )
      .min(10, "Phone number must be at least 10 digits"),
    additionalPhones: yup.array().of(
      yup
        .string()
        .matches(
          /^[0-9\s]+$/,
          "Phone number should only contain digits and spaces"
        )
        .min(10, "Phone number must be at least 10 digits")
    ),
  });

  const handleOpenChatModal = () => {
    setIsChatModalOpen(true);
  };

  const handleCloseChatModal = () => {
    setIsChatModalOpen(false);
  };

  const handleSaveChatSettings = (values: {
    chatType: string;
    phoneNumber: string;
  }) => {
    setChatSettings(values);
    console.log("Chat settings saved:", values);
    handleCloseChatModal();
  };

  const handleDeleteChatSettings = () => {
    setChatSettings({
      chatType: "",
      phoneNumber: "",
    });
    console.log("Chat settings deleted");
    handleCloseChatModal();
  };

  const chatSettingsValidationSchema = yup.object({
    chatType: yup.string().required("Chat type is required"),
    phoneNumber: yup
      .string()
      .required("Phone number is required")
      .matches(
        /^[0-9\s]+$/,
        "Phone number should only contain digits and spaces"
      )
      .min(10, "Phone number must be at least 10 digits"),
  });

  const handleOpenWebsiteModal = () => {
    setIsWebsiteModalOpen(true);
  };

  const handleCloseWebsiteModal = () => {
    setIsWebsiteModalOpen(false);
  };

  const handleSaveWebsite = (values: { websiteUrl: string }) => {
    setWebsiteUrl(values.websiteUrl);
    console.log("Website URL saved:", values.websiteUrl);
    handleCloseWebsiteModal();
  };

  const websiteValidationSchema = yup.object({
    websiteUrl: yup
      .string()
      .required("Website URL is required")
      .url("Please enter a valid URL (e.g., https://www.example.com)")
  });

  const handleOpenBusinessLocationModal = () => {
    setIsBusinessLocationModalOpen(true);
  };

  const handleCloseBusinessLocationModal = () => {
    setIsBusinessLocationModalOpen(false);
  };

  const handleSaveBusinessLocation = (values: any) => {
    setBusinessLocationData(values);
    console.log("Business location saved:", values);
    handleCloseBusinessLocationModal();
  };

  const handleAddAddressLine = (values: any, setFieldValue: any) => {
    const newLines = [...values.additionalAddressLines, ""];
    setFieldValue("additionalAddressLines", newLines);
  };

  const businessLocationValidationSchema = yup.object({
    country: yup.string().required("Country is required"),
    streetAddress: yup.string().required("Street address is required"),
    streetAddressLine2: yup.string(),
    additionalAddressLines: yup.array().of(yup.string()),
    city: yup.string().required("City is required"),
    pincode: yup.string().required("Pincode is required"),
    state: yup.string().required("State is required"),
  });

  const [isServiceAreaModalOpen, setIsServiceAreaModalOpen] = useState(false);
  const [serviceAreas, setServiceAreas] = useState([
    "Mathikere, Bengaluru, Karnataka, India",
    "Shivaji Nagar, Telangana 500018, India",
    "Yelahanka, Bengaluru, Karnataka, India",
    "Benson Town, Bengaluru, Karnataka, India",
    "Frazer Town, Bengaluru, Karnataka, India",
    "Kodigehalli, Bengaluru, Karnataka, India",
    "Sahakar Nagar, Bengaluru, Karnataka, India",
    "Vasanth Nagar, Bengaluru, Karnataka, India",
    "Vidyaranyapura, Bengaluru, Karnataka, India",
    "RT Nagar, Bengaluru, Karnataka 560032, India",
    "R.M.V. 2nd Stage, Bengaluru, Karnataka, India",
    "Anandnagar, Hebbal, Bengaluru, Karnataka 560024, India",
    "Ganganagar, RT Nagar, Bengaluru, Karnataka 560032, India",
    "Sadashiva Nagar, Armane Nagar, Bengaluru, Karnataka, India",
    "Munireddypalya, J.C.Nagar, Bengaluru, Karnataka 560006, India",
    "Chola Nagar, Anandnagar, Hebbal, Bengaluru, Karnataka 560024, India"
  ]);
  const [searchArea, setSearchArea] = useState("");

  const handleOpenServiceAreaModal = () => {
    setIsServiceAreaModalOpen(true);
  };

  const handleCloseServiceAreaModal = () => {
    setIsServiceAreaModalOpen(false);
  };

  const handleSaveServiceAreas = (values: any) => {
    setServiceAreas(values.serviceAreas);
    console.log("Service areas saved:", values.serviceAreas);
    handleCloseServiceAreaModal();
  };

  const handleRemoveServiceArea = (areaToRemove: any, setFieldValue: any, values: any) => {
    const updatedAreas = values.serviceAreas.filter((area: any) => area !== areaToRemove);
    setFieldValue("serviceAreas", updatedAreas);
  };

  const handleAddServiceArea = (newArea: any, setFieldValue: any, values: any) => {
    if (newArea && !values.serviceAreas.includes(newArea)) {
      setFieldValue("serviceAreas", [...values.serviceAreas, newArea]);
      setSearchArea("");
    }
  };

  const serviceAreaValidationSchema = yup.object({
    serviceAreas: yup.array().of(yup.string()).min(1, "At least one service area is required")
  });

  return (
    <Box sx={{ p: 3, maxWidth: "800px", margin: "0 auto" }}>
      <Typography variant="h4" gutterBottom>
        Business Category Demo
      </Typography>
      <Typography variant="body1" color="text.secondary" paragraph>
        This is a demo page to showcase the business category modal
      </Typography>

      <Card sx={{ mb: 4 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Demo Controls
          </Typography>
          <Divider sx={{ mb: 2 }} />

          <Box sx={{ mt: 3 }}>
            <Button
              variant="contained"
              color="primary"
              onClick={handleOpenAddCategoryModal}
              sx={{ textTransform: "none", mr: 2 }}
            >
              Open Add Business Category Modal
            </Button>
          </Box>
          {/* Edit Business */}
          <Box sx={{ mt: 3 }}>
            <Button
              variant="contained"
              color="primary"
              onClick={handleOpenBusinessNameModal}
              sx={{ textTransform: "none", mr: 2 }}
            >
              Edit Business
            </Button>
          </Box>
          {/* Business category */}
          <Divider sx={{ mb: 2, mt: 2 }} />
          <Box sx={{ mt: 3 }}>
            <Button
              variant="contained"
              color="primary"
              onClick={handleOpenCategoriesModal}
              sx={{ textTransform: "none", mr: 2 }}
            >
              Business Categories
            </Button>
          </Box>
          {/* Business Description */}
          <Divider sx={{ mb: 2, mt: 2 }} />
          <Box sx={{ mt: 3 }}>
            <Button
              variant="contained"
              color="primary"
              onClick={handleOpenDescriptionModal}
              sx={{ textTransform: "none", mr: 2 }}
            >
              Description
            </Button>
          </Box>
          {/* Business Opening Date*/}
          <Divider sx={{ mb: 2, mt: 2 }} />
          <Box sx={{ mt: 3 }}>
            <Button
              variant="contained"
              color="primary"
              onClick={handleOpenOpeningDateModal}
              sx={{ textTransform: "none", mr: 2 }}
            >
              Opening Date
            </Button>
          </Box>
          {/* Contact Info Phone Number*/}
          <Divider sx={{ mb: 2, mt: 2 }} />
          <Box sx={{ mt: 3 }}>
            <Button
              variant="contained"
              color="primary"
              onClick={handleOpenPhoneNumberModal}
              sx={{ textTransform: "none", mr: 2 }}
            >
              Phone Number
            </Button>
          </Box>
          {/* Chat */}
          <Divider sx={{ mb: 2, mt: 2 }} />
          <Box sx={{ mt: 3 }}>
            <Button
              variant="contained"
              color="primary"
              onClick={handleOpenChatModal}
              sx={{ textTransform: "none", mr: 2 }}
            >
              Chat
            </Button>
          </Box>
          {/* Website */}
          <Divider sx={{ mb: 2, mt: 2 }} />
          <Box sx={{ mt: 3 }}>
            <Button
              variant="contained"
              color="primary"
              onClick={handleOpenWebsiteModal}
              sx={{ textTransform: "none", mr: 2 }}
            >
              Website
            </Button>
          </Box>
          {/* Business Location */}
          <Divider sx={{ mb: 2, mt: 2 }} />
          <Box sx={{ mt: 3 }}>
            <Button
              variant="contained"
              color="primary"
              onClick={handleOpenBusinessLocationModal}
              sx={{ textTransform: "none", mr: 2 }}
            >
              Business Location
            </Button>
          </Box>
          {/* Service area */}
          <Divider sx={{ mb: 2, mt: 2 }} />
          <Box sx={{ mt: 3 }}>
            <Button
              variant="contained"
              color="primary"
              onClick={handleOpenServiceAreaModal}
              sx={{ textTransform: "none", mr: 2 }}
            >
              Service Area
            </Button>
          </Box>
          {/* Hours */}
          <Divider sx={{ mb: 2, mt: 2 }} />
          <Box>
            <BusinessHours
              onSave={(values) => {

              }}
            />
          </Box>

          {/* Special Hours */}
          <Divider sx={{ mb: 2, mt: 2 }} />
          <Box sx={{ mt: 3 }}>
            <AddSpecialHours
              onSave={(values) => {
                console.log("Special hours saved:", values);
              }}
            />
          </Box>

          {/* More From The Business */}
          <Divider sx={{ mb: 2, mt: 2 }} />
          <Box sx={{ mt: 3 }}>
            <FromTheBusiness
              onSave={(values) => {
                console.log("From the business saved:", values);
              }}
            />
          </Box>
          {/* More Accessibility */}
          <Divider sx={{ mb: 2, mt: 2 }} />
          <Box sx={{ mt: 3 }}>
            <MoreAccessibility
              onSave={(values: any) => {
                console.log("Accessibility saved:", values);
              }}
            />
          </Box>
          {/* More Amenities */}
          <Divider sx={{ mb: 2, mt: 2 }} />
          <Box sx={{ mt: 3 }}>
            <Amenities
              onSave={(values: any) => {
                console.log("Accessibility saved:", values);
              }}
            />
          </Box>
          {/* Add Children */}
          <Divider sx={{ mb: 2, mt: 2 }} />
          <Box sx={{ mt: 3 }}>
            <AddChildren
              onSave={(values: any) => {
                console.log("Accessibility saved:", values);
              }}
            />
          </Box>
          {/* Add Crowd */}
          <Divider sx={{ mb: 2, mt: 2 }} />
          <Box sx={{ mt: 3 }}>
            <Crowd
              onSave={(values: any) => {
                console.log("Accessibility saved:", values);
              }}
            />
          </Box>
          {/* Add Parking */}
          <Divider sx={{ mb: 2, mt: 2 }} />
          <Box sx={{ mt: 3 }}>
            <Parking
              onSave={(values: any) => {
                console.log("Accessibility saved:", values);
              }}
            />
          </Box>
          {/* Add Payments */}
          <Divider sx={{ mb: 2, mt: 2 }} />
          <Box sx={{ mt: 3 }}>
            <Payments
              onSave={(values: any) => {
                console.log("Accessibility saved:", values);
              }}
            />
          </Box>
          {/* Add Planning */}
          <Divider sx={{ mb: 2, mt: 2 }} />
          <Box sx={{ mt: 3 }}>
            <Planning
              onSave={(values: any) => {
                console.log("Accessibility saved:", values);
              }}
            />
          </Box>
          {/* Add ServiceOptions */}
          <Divider sx={{ mb: 2, mt: 2 }} />
          <Box sx={{ mt: 3 }}>
            <ServiceOptions
              onSave={(values: any) => {
                console.log("Accessibility saved:", values);
              }}
            />
          </Box>
        </CardContent>
      </Card>

      {/* AddBusinessCategoryModal */}
      <AddBusinessCategoryModal
        open={isAddCategoryModalOpen}
        onClose={handleCloseAddCategoryModal}
        onAddCategory={handleAddCategory}
      />

      {/* Edit Business Name Modal */}
      <EditBusinessNameModal
        open={isModalOpen}
        onClose={handleCloseBusniessNameModal}
        businessId={1} // Demo business ID
        currentBusinessName={businessName}
        onSuccess={() => {
          // In a real app, we would fetch the updated business data
          // For demo purposes, we'll just update the local state with the form value
          const inputElement = document.getElementById(
            "businessName"
          ) as HTMLInputElement;
          setBusinessName(inputElement?.value || businessName);
        }}
      />

      {/* Business Categories Modal */}
      <Dialog
        open={isCategoriesModalOpen}
        onClose={handleCloseCategoriesModal}
        fullWidth
        maxWidth="sm"
        PaperProps={{
          style: {
            backgroundColor: "white",
            color: "black",
            borderRadius: "8px",
          },
        }}
      >
        <DialogTitle
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            padding: { xs: "16px", sm: "20px 24px" },
            borderBottom: "1px solid rgba(0, 0, 0, 0.12)",
          }}
        >
          <Typography
            variant="h6"
            component="div"
            sx={{
              fontWeight: 600,
              color: "black",
              fontSize: { xs: "1.1rem", sm: "1.25rem" },
            }}
          >
            Business category
          </Typography>
          <IconButton
            edge="end"
            color="inherit"
            onClick={handleCloseCategoriesModal}
            aria-label="close"
          >
            <CloseIcon />
          </IconButton>
        </DialogTitle>

        <DialogContent sx={{ px: { xs: 2, sm: 3 }, py: { xs: 2, sm: 2 } }}>
          <Typography
            variant="body2"
            sx={{
              mb: 2,
              color: "rgba(0, 0, 0, 0.6)",
              fontSize: { xs: "0.875rem", sm: "1rem" },
            }}
          >
            Help customers find your business by industry.
            {/* <Button 
              sx={{ 
                p: 0, 
                minWidth: 'auto', 
                textTransform: 'none',
                fontSize: 'inherit',
                fontWeight: 'normal',
                verticalAlign: 'baseline',
                ml: 0.5
              }}
              color="primary"
            >
              Learn more
            </Button> */}
          </Typography>

          <Formik
            initialValues={{
              primaryCategory: categories.find(c => c.isPrimary)?.name || '',
              additionalCategories: categories.filter(c => !c.isPrimary).map(c => c.name)
            }}
            validationSchema={yup.object({
              primaryCategory: yup.string().required("Primary category is required"),
              additionalCategories: yup.array().of(yup.string())
            })}
            onSubmit={(values) => {
              const newCategories = [
                { name: values.primaryCategory, isPrimary: true },
                ...values.additionalCategories.filter(name => name.trim() !== '').map(name => ({ name, isPrimary: false }))
              ];
              setCategories(newCategories);
              handleCloseCategoriesModal();
            }}
          >
            {({ values, errors, touched, handleChange, handleBlur, isValid, setFieldValue }) => (
              <Form>
                <Box sx={{ mb: 3 }}>
                  <Typography
                    variant="body2"
                    sx={{
                      mb: 1,
                      color: "black",
                      fontSize: { xs: "0.875rem", sm: "1rem" },
                      fontWeight: 500
                    }}
                  >
                    Primary category
                  </Typography>
                  <TextField
                    fullWidth
                    name="primaryCategory"
                    value={values.primaryCategory}
                    onChange={handleChange}
                    onBlur={handleBlur}
                    error={touched.primaryCategory && Boolean(errors.primaryCategory)}
                    helperText={touched.primaryCategory && errors.primaryCategory}
                    sx={{ mb: 2 }}
                    InputProps={{
                      style: { color: "black" },
                    }}
                  />
                </Box>

                {values.additionalCategories.map((category, index) => (
                  <Box key={index} sx={{ mb: 3, position: 'relative' }}>
                    <Typography
                      variant="body2"
                      sx={{
                        mb: 1,
                        color: "black",
                        fontSize: { xs: "0.875rem", sm: "1rem" },
                        fontWeight: 500
                      }}
                    >
                      Additional category
                    </Typography>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <TextField
                        fullWidth
                        name={`additionalCategories[${index}]`}
                        value={values.additionalCategories[index]}
                        onChange={handleChange}
                        onBlur={handleBlur}
                        sx={{ mb: 1 }}
                        InputProps={{
                          style: { color: "black" },
                        }}
                      />
                      <IconButton
                        onClick={() => {
                          const newAdditionalCategories = [...values.additionalCategories];
                          newAdditionalCategories.splice(index, 1);
                          setFieldValue('additionalCategories', newAdditionalCategories);
                        }}
                        sx={{ ml: 1 }}
                        aria-label="remove category"
                      >
                        <CloseIcon />
                      </IconButton>
                    </Box>
                  </Box>
                ))}

                <Button
                  startIcon={<AddIcon />}
                  onClick={() => {
                    setFieldValue('additionalCategories', [...values.additionalCategories, '']);
                  }}
                  sx={{
                    mb: 3,
                    color: '#1976d2',
                    textTransform: 'none',
                    fontWeight: 'normal',
                    justifyContent: 'flex-start',
                    pl: 0
                  }}
                >
                  Add another category
                </Button>

                <Box sx={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  mt: 2,
                  borderTop: '1px solid rgba(0, 0, 0, 0.12)',
                  pt: 2
                }}>
                  <Button
                    variant="outlined"
                    onClick={handleCloseCategoriesModal}
                    sx={{
                      textTransform: 'none',
                      color: '#1976d2',
                      borderColor: '#1976d2'
                    }}
                  >
                    Cancel
                  </Button>
                  <Button
                    variant="contained"
                    type="submit"
                    sx={{
                      textTransform: 'none',
                      bgcolor: '#1976d2'
                    }}
                  >
                    Save
                  </Button>
                </Box>
              </Form>
            )}
          </Formik>
        </DialogContent>
      </Dialog>

      {/* Business Description Modal */}
      <Dialog
        open={isDescriptionModalOpen}
        onClose={handleCloseDescriptionModal}
        fullWidth
        maxWidth="sm"
        PaperProps={{
          style: {
            backgroundColor: "white",
            color: "black",
            borderRadius: "8px",
          },
        }}
      >
        <DialogTitle
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            padding: { xs: "16px", sm: "20px 24px" },
            borderBottom: "1px solid rgba(0, 0, 0, 0.12)",
          }}
        >
          <Typography
            variant="h6"
            component="div"
            sx={{
              fontWeight: 600,
              color: "black",
              fontSize: { xs: "1.1rem", sm: "1.25rem" },
            }}
          >
            Description
          </Typography>
          <IconButton
            edge="end"
            color="inherit"
            onClick={handleCloseDescriptionModal}
            aria-label="close"
          >
            <CloseIcon />
          </IconButton>
        </DialogTitle>

        <DialogContent sx={{ px: { xs: 2, sm: 3 }, py: { xs: 2, sm: 2 } }}>
          <Typography
            variant="body2"
            sx={{
              mb: 2,
              color: "rgba(0, 0, 0, 0.6)",
              fontSize: { xs: "0.875rem", sm: "1rem" },
            }}
          >
            Describe your business to customers on Google.
            {/* <Button 
              sx={{ 
                p: 0, 
                minWidth: 'auto', 
                textTransform: 'none',
                fontSize: 'inherit',
                fontWeight: 'normal',
                verticalAlign: 'baseline',
                ml: 0.5
              }}
              color="primary"
            >
              Learn more
            </Button> */}
          </Typography>

          <Formik
            initialValues={{
              description: businessDescription
            }}
            validationSchema={yup.object({
              description: yup.string()
                .required("Description is required")
                .min(10, "Description must be at least 10 characters")
                .max(750, "Description must be at most 750 characters")
            })}
            onSubmit={(values) => {
              setBusinessDescription(values.description);
              handleCloseDescriptionModal();
            }}
          >
            {({ values, errors, touched, handleChange, handleBlur, isValid }) => (
              <Form>
                <Box sx={{ mb: 3 }}>
                  <TextField
                    fullWidth
                    name="description"
                    value={values.description}
                    onChange={handleChange}
                    onBlur={handleBlur}
                    error={touched.description && Boolean(errors.description)}
                    helperText={touched.description && errors.description}
                    multiline
                    rows={10}
                    sx={{ mb: 1 }}
                    InputProps={{
                      style: { color: "black" },
                    }}
                  />
                  <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 1 }}>
                    <Typography
                      variant="caption"
                      sx={{
                        color: values.description.length > 750 ? 'error.main' : 'text.secondary'
                      }}
                    >
                      {values.description.length}/750
                    </Typography>
                  </Box>
                </Box>

                <Box sx={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  mt: 2,
                  borderTop: '1px solid rgba(0, 0, 0, 0.12)',
                  pt: 2
                }}>
                  <Button
                    variant="text"
                    startIcon={<EditIcon />}
                    sx={{
                      textTransform: 'none',
                      color: '#1976d2',
                    }}
                  >
                    Suggest description
                  </Button>
                  <Box>
                    <Button
                      variant="outlined"
                      onClick={handleCloseDescriptionModal}
                      sx={{
                        mr: 2,
                        textTransform: 'none',
                        color: '#1976d2',
                        borderColor: '#1976d2'
                      }}
                    >
                      Cancel
                    </Button>
                    <Button
                      variant="contained"
                      type="submit"
                      disabled={!isValid}
                      sx={{
                        textTransform: 'none',
                        bgcolor: '#1976d2'
                      }}
                    >
                      Save
                    </Button>
                  </Box>
                </Box>
              </Form>
            )}
          </Formik>
        </DialogContent>
      </Dialog>

      {/* Opening Date Modal */}
      <Dialog
        open={isOpeningDateModalOpen}
        onClose={handleCloseOpeningDateModal}
        fullWidth
        maxWidth="sm"
        PaperProps={{
          style: {
            backgroundColor: "white",
            borderRadius: "8px",
          },
        }}
      >
        <DialogTitle
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            padding: { xs: "16px", sm: "20px 24px" },
            borderBottom: "1px solid rgba(0, 0, 0, 0.12)",
            bgcolor: "white",
          }}
        >
          <Typography
            variant="h6"
            component="div"
            sx={{
              fontWeight: 500,
              color: "black",
              fontSize: { xs: "1.1rem", sm: "1.25rem" },
            }}
          >
            Opening date
          </Typography>
          <IconButton
            edge="end"
            color="inherit"
            onClick={handleCloseOpeningDateModal}
            aria-label="close"
          >
            <CloseIcon />
          </IconButton>
        </DialogTitle>

        <DialogContent sx={{ px: { xs: 2, sm: 3 }, py: { xs: 2, sm: 2 }, bgcolor: "white" }}>
          <Box sx={{ mb: 2 }}>
            <Typography
              variant="body2"
              sx={{
                color: "rgba(0, 0, 0, 0.7)",
                fontSize: { xs: "0.875rem", sm: "0.9rem" },
                display: "inline",
              }}
            >
              Add the date that you opened or will open at this address.{" "}
            </Typography>
            {/* <Typography
              component="span"
              sx={{
                color: "#1976d2",
                cursor: "pointer",
                fontSize: { xs: "0.875rem", sm: "0.9rem" },
                "&:hover": {
                  textDecoration: "underline",
                },
              }}
            >
              Learn more
            </Typography> */}
          </Box>

          <Formik
            initialValues={openingDate}
            validationSchema={openingDateValidationSchema}
            onSubmit={handleSaveOpeningDate}
          >
            {({ values, errors, touched, handleChange, handleBlur, isValid, handleSubmit }) => (
              <Form onSubmit={handleSubmit}>
                <Box
                  sx={{
                    display: 'flex',
                    flexDirection: { xs: 'column', sm: 'row' },
                    gap: 2,
                    mb: 3
                  }}
                >
                  {/* Year Field */}
                  <Box sx={{ flex: 1 }}>
                    <Typography
                      variant="body2"
                      sx={{
                        mb: 0.5,
                        color: "black",
                        fontWeight: 400,
                        fontSize: "0.8rem",
                      }}
                    >
                      Year*
                    </Typography>
                    <TextField
                      fullWidth
                      name="year"
                      value={values.year}
                      onChange={handleChange}
                      onBlur={handleBlur}
                      error={touched.year && Boolean(errors.year)}
                      sx={{
                        '& .MuiOutlinedInput-root': {
                          borderRadius: 1,
                          '& fieldset': {
                            borderColor: 'rgba(0, 0, 0, 0.23)',
                          },
                        },
                      }}
                      InputProps={{
                        style: { color: "black" },
                      }}
                    />
                    <FormHelperText
                      sx={{
                        color: touched.year && Boolean(errors.year) ? "error" : "rgba(0, 0, 0, 0.6)",
                        fontSize: "0.75rem",
                      }}
                    >
                      {touched.year && errors.year ? errors.year : "Required"}
                    </FormHelperText>
                  </Box>

                  {/* Month Field */}
                  <Box sx={{ flex: 1 }}>
                    <Typography
                      variant="body2"
                      sx={{
                        mb: 0.5,
                        color: "black",
                        fontWeight: 400,
                        fontSize: "0.8rem",
                      }}
                    >
                      Month*
                    </Typography>
                    <FormControl
                      fullWidth
                      error={touched.month && Boolean(errors.month)}
                    >
                      <Select
                        name="month"
                        value={values.month}
                        onChange={handleChange}
                        onBlur={handleBlur}
                        displayEmpty
                        sx={{
                          color: "black",
                          borderRadius: 1,
                          '& .MuiOutlinedInput-notchedOutline': {
                            borderColor: 'rgba(0, 0, 0, 0.23)',
                          },
                        }}
                      >
                        {months.map((month) => (
                          <MenuItem key={month} value={month}>
                            {month}
                          </MenuItem>
                        ))}
                      </Select>
                      <FormHelperText
                        sx={{
                          color: touched.month && Boolean(errors.month) ? "error" : "rgba(0, 0, 0, 0.6)",
                          fontSize: "0.75rem",
                        }}
                      >
                        {touched.month && errors.month ? errors.month : "Required"}
                      </FormHelperText>
                    </FormControl>
                  </Box>

                  {/* Day Field */}
                  <Box sx={{ flex: 1 }}>
                    <Typography
                      variant="body2"
                      sx={{
                        mb: 0.5,
                        color: "black",
                        fontWeight: 400,
                        fontSize: "0.8rem",
                      }}
                    >
                      Day
                    </Typography>
                    <FormControl fullWidth>
                      <Select
                        name="day"
                        value={values.day}
                        onChange={handleChange}
                        onBlur={handleBlur}
                        displayEmpty
                        sx={{
                          color: "black",
                          borderRadius: 1,
                          '& .MuiOutlinedInput-notchedOutline': {
                            borderColor: 'rgba(0, 0, 0, 0.23)',
                          },
                        }}
                      >
                        <MenuItem value="None">None</MenuItem>
                        {days.map((day) => (
                          <MenuItem key={day} value={day}>
                            {day}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Box>
                </Box>

                <Divider sx={{ my: 2 }} />

                <Box sx={{
                  display: 'flex',
                  justifyContent: 'flex-start',
                  mt: 2,
                  flexWrap: 'wrap',
                  gap: 1
                }}>
                  <Button
                    variant="contained"
                    type="submit"
                    disabled={!isValid}
                    sx={{
                      textTransform: 'none',
                      bgcolor: '#1976d2',
                      borderRadius: 1,
                      px: 3,
                      color: 'white',
                      '&:hover': {
                        bgcolor: '#1565c0',
                      }
                    }}
                  >
                    Save
                  </Button>
                  <Button
                    variant="outlined"
                    onClick={handleCloseOpeningDateModal}
                    sx={{
                      textTransform: 'none',
                      color: '#1976d2',
                      borderColor: '#e0e0e0',
                      borderRadius: 1,
                      px: 3,
                      bgcolor: 'white',
                    }}
                  >
                    Cancel
                  </Button>
                  <Button
                    variant="text"
                    onClick={handleDeleteOpeningDate}
                    sx={{
                      textTransform: 'none',
                      color: '#1976d2',
                      borderRadius: 1,
                      px: 3,
                    }}
                  >
                    Delete
                  </Button>
                </Box>
              </Form>
            )}
          </Formik>
        </DialogContent>
      </Dialog>

      {/* Phone Number Modal */}
      <Dialog
        open={isPhoneNumberModalOpen}
        onClose={handleClosePhoneNumberModal}
        fullWidth
        maxWidth="sm"
        PaperProps={{
          style: {
            backgroundColor: "white",
            borderRadius: "8px",
          },
        }}
      >
        <DialogTitle
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            padding: { xs: "16px", sm: "20px 24px" },
            borderBottom: "1px solid rgba(0, 0, 0, 0.12)",
            bgcolor: "white",
          }}
        >
          <Typography
            variant="h6"
            component="div"
            sx={{
              fontWeight: 500,
              color: "black",
              fontSize: { xs: "1.1rem", sm: "1.25rem" },
            }}
          >
            Contact information
          </Typography>
          <IconButton
            edge="end"
            color="inherit"
            onClick={handleClosePhoneNumberModal}
            aria-label="close"
          >
            <CloseIcon />
          </IconButton>
        </DialogTitle>

        <DialogContent sx={{ px: { xs: 2, sm: 3 }, py: { xs: 2, sm: 2 }, bgcolor: "white" }}>
          <Box sx={{ mb: 3 }}>
            <Typography
              variant="h6"
              sx={{
                color: "black",
                fontSize: "1rem",
                fontWeight: 500,
                mb: 0.5,
              }}
            >
              Phone number
            </Typography>
            <Typography
              variant="body2"
              sx={{
                color: "rgba(0, 0, 0, 0.7)",
                fontSize: { xs: "0.875rem", sm: "0.9rem" },
                display: "inline",
              }}
            >
              Provide a number that connects directly to your business.{" "}
            </Typography>
            {/* <Typography
              component="span"
              sx={{
                color: "#1976d2",
                cursor: "pointer",
                fontSize: { xs: "0.875rem", sm: "0.9rem" },
                "&:hover": {
                  textDecoration: "underline",
                },
              }}
            >
              Learn more
            </Typography> */}
          </Box>

          <Formik
            initialValues={phoneNumbers}
            validationSchema={phoneNumberValidationSchema}
            onSubmit={handleSavePhoneNumbers}
          >
            {({ values, errors, touched, handleChange, handleBlur, isValid, handleSubmit, setFieldValue }) => (
              <Form onSubmit={handleSubmit}>
                {/* Primary Phone */}
                <Box sx={{ mb: 3 }}>
                  <Typography
                    variant="body2"
                    sx={{
                      mb: 1,
                      color: "black",
                      fontWeight: 400,
                      fontSize: "0.9rem",
                    }}
                  >
                    Primary phone
                  </Typography>
                  <Box sx={{ display: 'flex', gap: 1 }}>
                    <Box
                      sx={{
                        width: '80px',
                        border: '1px solid rgba(0, 0, 0, 0.23)',
                        borderRadius: 1,
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'space-between',
                        px: 1,
                        py: 0.5,
                      }}
                    >
                      <Box
                        component="img"
                        src="https://flagcdn.com/w20/in.png"
                        alt="India"
                        sx={{ width: 24, height: 16 }}
                      />
                      <Box
                        component="span"
                        sx={{
                          color: 'black',
                          fontSize: '0.9rem',
                        }}
                      >
                        ▼
                      </Box>
                    </Box>
                    <TextField
                      fullWidth
                      name="primaryPhone"
                      value={values.primaryPhone}
                      onChange={handleChange}
                      onBlur={handleBlur}
                      error={touched.primaryPhone && Boolean(errors.primaryPhone)}
                      helperText={touched.primaryPhone && errors.primaryPhone}
                      placeholder="Phone number"
                      sx={{
                        '& .MuiOutlinedInput-root': {
                          borderRadius: 1,
                          '& fieldset': {
                            borderColor: 'rgba(0, 0, 0, 0.23)',
                          },
                        },
                      }}
                      InputProps={{
                        style: { color: "black" },
                      }}
                    />
                  </Box>
                </Box>

                {/* Additional Phones */}
                {values.additionalPhones.map((phone, index) => (
                  <Box key={index} sx={{ mb: 3 }}>
                    <Typography
                      variant="body2"
                      sx={{
                        mb: 1,
                        color: "black",
                        fontWeight: 400,
                        fontSize: "0.9rem",
                      }}
                    >
                      Additional phone
                    </Typography>
                    <Box sx={{ display: 'flex', gap: 1, alignItems: 'center' }}>
                      <Box
                        sx={{
                          width: '80px',
                          border: '1px solid rgba(0, 0, 0, 0.23)',
                          borderRadius: 1,
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'space-between',
                          px: 1,
                          py: 0.5,
                        }}
                      >
                        <Box
                          component="img"
                          src="https://flagcdn.com/w20/in.png"
                          alt="India"
                          sx={{ width: 24, height: 16 }}
                        />
                        <Box
                          component="span"
                          sx={{
                            color: 'black',
                            fontSize: '0.9rem',
                          }}
                        >
                          ▼
                        </Box>
                      </Box>
                      <TextField
                        fullWidth
                        name={`additionalPhones[${index}]`}
                        value={phone}
                        onChange={handleChange}
                        onBlur={handleBlur}
                        error={touched.additionalPhones &&
                          Array.isArray(errors.additionalPhones) &&
                          Boolean(errors.additionalPhones[index])}
                        helperText={touched.additionalPhones &&
                          Array.isArray(errors.additionalPhones) &&
                          errors.additionalPhones[index]}
                        placeholder="Phone number"
                        sx={{
                          '& .MuiOutlinedInput-root': {
                            borderRadius: 1,
                            '& fieldset': {
                              borderColor: 'rgba(0, 0, 0, 0.23)',
                            },
                          },
                        }}
                        InputProps={{
                          style: { color: "black" },
                        }}
                      />
                      <IconButton
                        onClick={() => {
                          const newPhones = [...values.additionalPhones];
                          newPhones.splice(index, 1);
                          setFieldValue('additionalPhones', newPhones);
                        }}
                        sx={{ color: 'rgba(0, 0, 0, 0.54)' }}
                      >
                        <ClearIcon />
                      </IconButton>
                    </Box>
                  </Box>
                ))}

                {/* Add Phone Button */}
                <Button
                  startIcon={<AddIcon />}
                  onClick={() => {
                    setFieldValue('additionalPhones', [...values.additionalPhones, '']);
                  }}
                  sx={{
                    mb: 3,
                    color: '#1976d2',
                    textTransform: 'none',
                    fontWeight: 'normal',
                    justifyContent: 'flex-start',
                    pl: 0
                  }}
                >
                  Add phone number
                </Button>

                <Divider sx={{ my: 2 }} />

                <Box sx={{
                  display: 'flex',
                  justifyContent: 'flex-start',
                  mt: 2,
                  flexWrap: 'wrap',
                  gap: 1
                }}>
                  <Button
                    variant="contained"
                    type="submit"
                    disabled={!isValid}
                    sx={{
                      textTransform: 'none',
                      bgcolor: '#1976d2',
                      borderRadius: 1,
                      px: 3,
                      color: 'white',
                      '&:hover': {
                        bgcolor: '#1565c0',
                      }
                    }}
                  >
                    Save
                  </Button>
                  <Button
                    variant="outlined"
                    onClick={handleClosePhoneNumberModal}
                    sx={{
                      textTransform: 'none',
                      color: '#1976d2',
                      borderColor: '#e0e0e0',
                      borderRadius: 1,
                      px: 3,
                      bgcolor: 'white',
                    }}
                  >
                    Cancel
                  </Button>
                </Box>
              </Form>
            )}
          </Formik>
        </DialogContent>
      </Dialog>

      {/* Chat Modal */}
      <Dialog
        open={isChatModalOpen}
        onClose={handleCloseChatModal}
        fullWidth
        maxWidth="sm"
        PaperProps={{
          style: {
            backgroundColor: "white",
            borderRadius: "8px",
          },
        }}
      >
        <DialogTitle
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            padding: { xs: "16px", sm: "20px 24px" },
            borderBottom: "1px solid rgba(0, 0, 0, 0.12)",
            bgcolor: "white",
          }}
        >
          <Typography
            variant="h6"
            component="div"
            sx={{
              fontWeight: 500,
              color: "black",
              fontSize: { xs: "1.1rem", sm: "1.25rem" },
            }}
          >
            Chat
          </Typography>
          <IconButton
            edge="end"
            color="inherit"
            onClick={handleCloseChatModal}
            aria-label="close"
          >
            <CloseIcon />
          </IconButton>
        </DialogTitle>

        <DialogContent sx={{ px: { xs: 2, sm: 3 }, py: { xs: 2, sm: 2 }, bgcolor: "white" }}>
          <Box sx={{ mb: 3 }}>
            <Typography
              variant="body2"
              sx={{
                color: "rgba(0, 0, 0, 0.7)",
                fontSize: { xs: "0.875rem", sm: "0.9rem" },
                display: "inline",
                mb: 2,
              }}
            >
              Allow customers to chat with your business via SMS or other apps.{" "}
            </Typography>
            {/* <Typography
              component="span"
              sx={{
                color: "#1976d2",
                cursor: "pointer",
                fontSize: { xs: "0.875rem", sm: "0.9rem" },
                "&:hover": {
                  textDecoration: "underline",
                },
              }}
            >
              Learn more
            </Typography> */}
          </Box>

          <Formik
            initialValues={chatSettings}
            validationSchema={chatSettingsValidationSchema}
            onSubmit={handleSaveChatSettings}
          >
            {({ values, errors, touched, handleChange, handleBlur, isValid, handleSubmit }) => (
              <Form onSubmit={handleSubmit}>
                <Box
                  sx={{
                    display: 'flex',
                    flexDirection: { xs: 'column', sm: 'row' },
                    gap: 2,
                    mb: 3,
                    alignItems: 'flex-start',
                  }}
                >
                  {/* Chat Type Selector */}
                  <Box sx={{ minWidth: '200px' }}>
                    <Typography
                      variant="body2"
                      sx={{
                        mb: 0.5,
                        color: "black",
                        fontWeight: 400,
                        fontSize: "0.8rem",
                      }}
                    >
                      Chat
                    </Typography>
                    <FormControl
                      fullWidth
                      error={touched.chatType && Boolean(errors.chatType)}
                    >
                      <Select
                        name="chatType"
                        value={values.chatType}
                        onChange={handleChange}
                        onBlur={handleBlur}
                        displayEmpty
                        renderValue={(selected) => (
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            <TextsmsIcon sx={{ fontSize: 20 }} />
                            <Typography>{selected}</Typography>
                          </Box>
                        )}
                        sx={{
                          color: "black",
                          borderRadius: 1,
                          '& .MuiOutlinedInput-notchedOutline': {
                            borderColor: 'rgba(0, 0, 0, 0.23)',
                          },
                        }}
                      >
                        <MenuItem value="Text message">
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            <TextsmsIcon sx={{ fontSize: 20 }} />
                            <Typography>Text message</Typography>
                          </Box>
                        </MenuItem>
                      </Select>
                      {touched.chatType && errors.chatType && (
                        <FormHelperText>{errors.chatType}</FormHelperText>
                      )}
                    </FormControl>
                  </Box>

                  {/* Country Code Selector */}
                  <Box sx={{ width: '80px' }}>
                    <Typography
                      variant="body2"
                      sx={{
                        mb: 0.5,
                        color: "black",
                        fontWeight: 400,
                        fontSize: "0.8rem",
                        visibility: 'hidden', // Hidden but keeps alignment
                      }}
                    >
                      Country
                    </Typography>
                    <Box
                      sx={{
                        width: '100%',
                        height: '56px',
                        border: '1px solid rgba(0, 0, 0, 0.23)',
                        borderRadius: 1,
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'space-between',
                        px: 1,
                      }}
                    >
                      <Box
                        component="img"
                        src="https://flagcdn.com/w20/in.png"
                        alt="India"
                        sx={{ width: 24, height: 16 }}
                      />
                      <Box
                        component="span"
                        sx={{
                          color: 'black',
                          fontSize: '0.9rem',
                        }}
                      >
                        ▼
                      </Box>
                    </Box>
                  </Box>

                  {/* Phone Number Field */}
                  <Box sx={{ flex: 1 }}>
                    <Typography
                      variant="body2"
                      sx={{
                        mb: 0.5,
                        color: "black",
                        fontWeight: 400,
                        fontSize: "0.8rem",
                        visibility: 'hidden', // Hidden but keeps alignment
                      }}
                    >
                      Phone
                    </Typography>
                    <TextField
                      fullWidth
                      name="phoneNumber"
                      value={values.phoneNumber}
                      onChange={handleChange}
                      onBlur={handleBlur}
                      error={touched.phoneNumber && Boolean(errors.phoneNumber)}
                      helperText={touched.phoneNumber && errors.phoneNumber}
                      placeholder="Phone number"
                      sx={{
                        '& .MuiOutlinedInput-root': {
                          borderRadius: 1,
                          '& fieldset': {
                            borderColor: 'rgba(0, 0, 0, 0.23)',
                          },
                        },
                      }}
                      InputProps={{
                        style: { color: "black" },
                      }}
                    />
                  </Box>
                </Box>

                <Divider sx={{ my: 2 }} />

                <Box sx={{
                  display: 'flex',
                  justifyContent: 'flex-start',
                  mt: 2,
                  flexWrap: 'wrap',
                  gap: 1
                }}>
                  <Button
                    variant="contained"
                    type="submit"
                    disabled={!isValid}
                    sx={{
                      textTransform: 'none',
                      bgcolor: '#1976d2',
                      borderRadius: 1,
                      px: 3,
                      color: 'white',
                      '&:hover': {
                        bgcolor: '#1565c0',
                      }
                    }}
                  >
                    Save
                  </Button>
                  <Button
                    variant="outlined"
                    onClick={handleCloseChatModal}
                    sx={{
                      textTransform: 'none',
                      color: '#1976d2',
                      borderColor: '#e0e0e0',
                      borderRadius: 1,
                      px: 3,
                      bgcolor: 'white',
                    }}
                  >
                    Cancel
                  </Button>
                  <Button
                    variant="text"
                    onClick={handleDeleteChatSettings}
                    sx={{
                      textTransform: 'none',
                      color: '#1976d2',
                      borderRadius: 1,
                      px: 3,
                    }}
                  >
                    Delete
                  </Button>
                </Box>
              </Form>
            )}
          </Formik>
        </DialogContent>
      </Dialog>

      {/* Website Modal */}
      <Dialog
        open={isWebsiteModalOpen}
        onClose={handleCloseWebsiteModal}
        fullWidth
        maxWidth="sm"
        PaperProps={{
          style: {
            backgroundColor: "white",
            borderRadius: "8px",
          },
        }}
      >
        <DialogTitle
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            padding: { xs: "16px", sm: "20px 24px" },
            borderBottom: "1px solid rgba(0, 0, 0, 0.12)",
            bgcolor: "white",
          }}
        >
          <Typography
            variant="h6"
            component="div"
            sx={{
              fontWeight: 500,
              color: "black",
              fontSize: { xs: "1.1rem", sm: "1.25rem" },
            }}
          >
            Website
          </Typography>
          <IconButton
            edge="end"
            color="inherit"
            onClick={handleCloseWebsiteModal}
            aria-label="close"
          >
            <CloseIcon />
          </IconButton>
        </DialogTitle>

        <DialogContent sx={{ px: { xs: 2, sm: 3 }, py: { xs: 2, sm: 2 }, bgcolor: "white" }}>
          <Box sx={{ mb: 3 }}>
            <Typography
              variant="body2"
              sx={{
                color: "rgba(0, 0, 0, 0.7)",
                fontSize: { xs: "0.875rem", sm: "0.9rem" },
                display: "inline",
                mb: 2,
              }}
            >
              Add the link to your website.{" "}
            </Typography>
            {/* <Typography
              component="span"
              sx={{
                color: "#1976d2",
                cursor: "pointer",
                fontSize: { xs: "0.875rem", sm: "0.9rem" },
                "&:hover": {
                  textDecoration: "underline",
                },
              }}
            >
              Learn more
            </Typography> */}
          </Box>

          <Formik
            initialValues={{ websiteUrl }}
            validationSchema={websiteValidationSchema}
            onSubmit={handleSaveWebsite}
          >
            {({ values, errors, touched, handleChange, handleBlur, isValid, handleSubmit }) => (
              <Form onSubmit={handleSubmit}>
                <TextField
                  fullWidth
                  name="websiteUrl"
                  value={values.websiteUrl}
                  onChange={handleChange}
                  onBlur={handleBlur}
                  error={touched.websiteUrl && Boolean(errors.websiteUrl)}
                  helperText={touched.websiteUrl && errors.websiteUrl}
                  placeholder="https://www.example.com/"
                  sx={{
                    mb: 3,
                    '& .MuiOutlinedInput-root': {
                      borderRadius: 1,
                      '& fieldset': {
                        borderColor: 'rgba(0, 0, 0, 0.23)',
                      },
                    },
                  }}
                  InputProps={{
                    style: { color: "black" },
                  }}
                />

                <Divider sx={{ my: 2 }} />

                <Box sx={{
                  display: 'flex',
                  justifyContent: 'flex-start',
                  mt: 2,
                  flexWrap: 'wrap',
                  gap: 1
                }}>
                  <Button
                    variant="contained"
                    type="submit"
                    disabled={!isValid}
                    sx={{
                      textTransform: 'none',
                      bgcolor: '#1976d2',
                      borderRadius: 1,
                      px: 3,
                      color: 'white',
                      '&:hover': {
                        bgcolor: '#1565c0',
                      }
                    }}
                  >
                    Save
                  </Button>
                  <Button
                    variant="outlined"
                    onClick={handleCloseWebsiteModal}
                    sx={{
                      textTransform: 'none',
                      color: '#1976d2',
                      borderColor: '#e0e0e0',
                      borderRadius: 1,
                      px: 3,
                      bgcolor: 'white',
                    }}
                  >
                    Cancel
                  </Button>
                </Box>
              </Form>
            )}
          </Formik>
        </DialogContent>
      </Dialog>

      {/* Business Location Modal */}
      <Dialog
        open={isBusinessLocationModalOpen}
        onClose={handleCloseBusinessLocationModal}
        fullWidth
        maxWidth="md"
        PaperProps={{
          style: {
            backgroundColor: "white",
            borderRadius: "8px",
          },
        }}
      >
        <DialogTitle
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            padding: { xs: "16px", sm: "20px 24px" },
            borderBottom: "1px solid rgba(0, 0, 0, 0.12)",
            bgcolor: "white",
          }}
        >
          <Typography
            variant="h6"
            component="div"
            sx={{
              fontWeight: 500,
              color: "black",
              fontSize: { xs: "1.1rem", sm: "1.25rem" },
            }}
          >
            Location and areas
          </Typography>
          <IconButton
            edge="end"
            color="inherit"
            onClick={handleCloseBusinessLocationModal}
            aria-label="close"
          >
            <CloseIcon />
          </IconButton>
        </DialogTitle>

        <DialogContent sx={{ px: { xs: 2, sm: 3 }, py: { xs: 2, sm: 2 }, bgcolor: "white" }}>
          <Box sx={{ mb: 3 }}>
            <Typography
              variant="h6"
              sx={{
                color: "black",
                fontSize: "1rem",
                fontWeight: 500,
                mb: 1,
              }}
            >
              Business location
            </Typography>

            <Typography
              variant="body2"
              sx={{
                color: "rgba(0, 0, 0, 0.7)",
                fontSize: { xs: "0.875rem", sm: "0.9rem" },
                display: "inline",
                mb: 2,
              }}
            >
              If customers visit your business, add an address and adjust the pin on the map to its location.{" "}
            </Typography>
            {/* <Typography
              component="span"
              sx={{
                color: "#1976d2",
                cursor: "pointer",
                fontSize: { xs: "0.875rem", sm: "0.9rem" },
                "&:hover": {
                  textDecoration: "underline",
                },
              }}
            >
              Learn more
            </Typography> */}
          </Box>

          <Formik
            initialValues={businessLocationData}
            validationSchema={businessLocationValidationSchema}
            onSubmit={handleSaveBusinessLocation}
          >
            {({ values, errors, touched, handleChange, handleBlur, isValid, handleSubmit, setFieldValue }) => (
              <Form onSubmit={handleSubmit}>
                <FormControlLabel
                  control={
                    <Checkbox
                      checked={values.showAddress}
                      onChange={(e) => setFieldValue("showAddress", e.target.checked)}
                      sx={{
                        color: "#1976d2",
                        '&.Mui-checked': {
                          color: "#1976d2",
                        },
                      }}
                    />
                  }
                  label={
                    <Typography sx={{ color: "black", fontSize: "0.9rem" }}>
                      Show business address to customers
                    </Typography>
                  }
                  sx={{ mb: 2 }}
                />

                <Grid container spacing={2}>
                  <Grid item xs={12} md={6}>
                    {/* Left Column - Form Fields */}
                    <Box sx={{ mb: 2 }}>
                      <Typography
                        variant="body2"
                        sx={{
                          mb: 0.5,
                          color: "black",
                          fontWeight: 400,
                          fontSize: "0.8rem",
                        }}
                      >
                        Country/Region
                      </Typography>
                      <TextField
                        fullWidth
                        name="country"
                        value={values.country}
                        onChange={handleChange}
                        onBlur={handleBlur}
                        error={touched.country && Boolean(errors.country)}
                        helperText={
                  touched.country &&
                  typeof errors.country === "string"
                    ? errors.country
                    : undefined
                }
                        sx={{
                          '& .MuiOutlinedInput-root': {
                            borderRadius: 1,
                            '& fieldset': {
                              borderColor: 'rgba(0, 0, 0, 0.23)',
                            },
                          },
                        }}
                        InputProps={{
                          style: { color: "black" },
                          endAdornment: (
                            <InputAdornment position="end">
                              <InfoOutlinedIcon sx={{ color: 'rgba(0, 0, 0, 0.54)' }} />
                            </InputAdornment>
                          ),
                        }}
                      />
                    </Box>

                    <Box sx={{ mb: 2 }}>
                      <Typography
                        variant="body2"
                        sx={{
                          mb: 0.5,
                          color: "black",
                          fontWeight: 400,
                          fontSize: "0.8rem",
                        }}
                      >
                        Street address
                      </Typography>
                      <TextField
                        fullWidth
                        name="streetAddress"
                        value={values.streetAddress}
                        onChange={handleChange}
                        onBlur={handleBlur}
                        error={touched.streetAddress && Boolean(errors.streetAddress)}
                        helperText={touched.streetAddress && errors.streetAddress}
                        sx={{
                          '& .MuiOutlinedInput-root': {
                            borderRadius: 1,
                            '& fieldset': {
                              borderColor: 'rgba(0, 0, 0, 0.23)',
                            },
                          },
                        }}
                        InputProps={{
                          style: { color: "black" },
                        }}
                      />
                    </Box>

                    <Box sx={{ mb: 2 }}>
                      <Typography
                        variant="body2"
                        sx={{
                          mb: 0.5,
                          color: "black",
                          fontWeight: 400,
                          fontSize: "0.8rem",
                        }}
                      >
                        Street address line 2 (optional)
                      </Typography>
                      <TextField
                        fullWidth
                        name="streetAddressLine2"
                        value={values.streetAddressLine2}
                        onChange={handleChange}
                        onBlur={handleBlur}
                        error={touched.streetAddressLine2 && Boolean(errors.streetAddressLine2)}
                        helperText={touched.streetAddressLine2 && errors.streetAddressLine2}
                        sx={{
                          '& .MuiOutlinedInput-root': {
                            borderRadius: 1,
                            '& fieldset': {
                              borderColor: 'rgba(0, 0, 0, 0.23)',
                            },
                          },
                        }}
                        InputProps={{
                          style: { color: "black" },
                        }}
                      />
                    </Box>

                    {/* Additional Address Lines */}
                    {values.additionalAddressLines.map((line, index) => (
                      <Box key={index} sx={{ mb: 2 }}>
                        <Typography
                          variant="body2"
                          sx={{
                            mb: 0.5,
                            color: "black",
                            fontWeight: 400,
                            fontSize: "0.8rem",
                          }}
                        >
                          Street address line {index + 3} (optional)
                        </Typography>
                        <TextField
                          fullWidth
                          name={`additionalAddressLines[${index}]`}
                          value={line}
                          onChange={handleChange}
                          onBlur={handleBlur}
                          sx={{
                            '& .MuiOutlinedInput-root': {
                              borderRadius: 1,
                              '& fieldset': {
                                borderColor: 'rgba(0, 0, 0, 0.23)',
                              },
                            },
                          }}
                          InputProps={{
                            style: { color: "black" },
                          }}
                        />
                      </Box>
                    ))}

                    {/* Add Address Line Button */}
                    <Button
                      startIcon={<AddIcon />}
                      onClick={() => handleAddAddressLine(values, setFieldValue)}
                      sx={{
                        color: '#1976d2',
                        textTransform: 'none',
                        mb: 2,
                        p: 0,
                        '&:hover': {
                          backgroundColor: 'transparent',
                          textDecoration: 'underline',
                        },
                      }}
                    >
                      Add address line (optional)
                    </Button>

                    <Box sx={{ mb: 2 }}>
                      <Typography
                        variant="body2"
                        sx={{
                          mb: 0.5,
                          color: "black",
                          fontWeight: 400,
                          fontSize: "0.8rem",
                        }}
                      >
                        Town/City
                      </Typography>
                      <TextField
                        fullWidth
                        name="city"
                        value={values.city}
                        onChange={handleChange}
                        onBlur={handleBlur}
                        error={touched.city && Boolean(errors.city)}
                        helperText={touched.city && errors.city}
                        sx={{
                          '& .MuiOutlinedInput-root': {
                            borderRadius: 1,
                            '& fieldset': {
                              borderColor: 'rgba(0, 0, 0, 0.23)',
                            },
                          },
                        }}
                        InputProps={{
                          style: { color: "black" },
                        }}
                      />
                    </Box>

                    <Box sx={{ mb: 2 }}>
                      <Typography
                        variant="body2"
                        sx={{
                          mb: 0.5,
                          color: "black",
                          fontWeight: 400,
                          fontSize: "0.8rem",
                        }}
                      >
                        Pincode
                      </Typography>
                      <TextField
                        fullWidth
                        name="pincode"
                        value={values.pincode}
                        onChange={handleChange}
                        onBlur={handleBlur}
                        error={touched.pincode && Boolean(errors.pincode)}
                        helperText={touched.pincode && errors.pincode}
                        sx={{
                          '& .MuiOutlinedInput-root': {
                            borderRadius: 1,
                            '& fieldset': {
                              borderColor: 'rgba(0, 0, 0, 0.23)',
                            },
                          },
                        }}
                        InputProps={{
                          style: { color: "black" },
                        }}
                      />
                    </Box>

                    <Box sx={{ mb: 2 }}>
                      <Typography
                        variant="body2"
                        sx={{
                          mb: 0.5,
                          color: "black",
                          fontWeight: 400,
                          fontSize: "0.8rem",
                        }}
                      >
                        State
                      </Typography>
                      <TextField
                        fullWidth
                        select
                        name="state"
                        value={values.state}
                        onChange={handleChange}
                        onBlur={handleBlur}
                        error={touched.state && Boolean(errors.state)}
                        helperText={touched.state && errors.state}
                        sx={{
                          '& .MuiOutlinedInput-root': {
                            borderRadius: 1,
                            '& fieldset': {
                              borderColor: 'rgba(0, 0, 0, 0.23)',
                            },
                          },
                        }}
                        InputProps={{
                          style: { color: "black" },
                        }}
                      >
                        <MenuItem value="Karnataka">Karnataka</MenuItem>
                        <MenuItem value="Tamil Nadu">Tamil Nadu</MenuItem>
                        <MenuItem value="Maharashtra">Maharashtra</MenuItem>
                        <MenuItem value="Delhi">Delhi</MenuItem>
                      </TextField>
                    </Box>
                  </Grid>

                  <Grid item xs={12} md={6}>
                    {/* Right Column - Map */}
                    <Box
                      sx={{
                        width: '100%',
                        height: { xs: '300px', sm: '400px' },
                        borderRadius: 1,
                        overflow: 'hidden',
                        border: '1px solid rgba(0, 0, 0, 0.12)',
                        position: 'relative',
                        mb: 2
                      }}
                    >
                      <Box
                        component="img"
                        src="https://maps.googleapis.com/maps/api/staticmap?center=13.0234,77.5938&zoom=15&size=800x600&markers=color:red%7C13.0234,77.5938&key=YOUR_API_KEY"
                        alt="Map location"
                        sx={{
                          width: '100%',
                          height: '100%',
                          objectFit: 'cover'
                        }}
                      />
                      <Box
                        sx={{
                          position: 'absolute',
                          top: 10,
                          right: 10,
                          padding: '6px 12px',
                          backgroundColor: 'white',
                          borderRadius: 1,
                          boxShadow: '0 2px 4px rgba(0,0,0,0.2)',
                          cursor: 'pointer',
                          color: '#1976d2',
                          fontSize: '14px',
                          fontWeight: 500
                        }}
                      >
                        Adjust
                      </Box>
                      <Box
                        sx={{
                          position: 'absolute',
                          bottom: 0,
                          left: 0,
                          right: 0,
                          display: 'flex',
                          justifyContent: 'space-between',
                          padding: '8px',
                          backgroundColor: 'rgba(255, 255, 255, 0.8)',
                          fontSize: '12px'
                        }}
                      >
                        <Typography variant="caption">Keyboard shortcuts</Typography>
                        <Typography variant="caption">Map data ©2025</Typography>
                        <Typography variant="caption">Terms</Typography>
                      </Box>
                    </Box>
                  </Grid>
                </Grid>

                <Divider sx={{ my: 2 }} />

                <Box sx={{
                  display: 'flex',
                  justifyContent: 'flex-start',
                  mt: 2,
                  flexWrap: 'wrap',
                  gap: 1
                }}>
                  <Button
                    variant="contained"
                    type="submit"
                    disabled={!isValid}
                    sx={{
                      textTransform: 'none',
                      bgcolor: '#1976d2',
                      borderRadius: 1,
                      px: 3,
                      color: 'white',
                      '&:hover': {
                        bgcolor: '#1565c0',
                      }
                    }}
                  >
                    Save
                  </Button>
                  <Button
                    variant="outlined"
                    onClick={handleCloseBusinessLocationModal}
                    sx={{
                      textTransform: 'none',
                      color: '#1976d2',
                      borderColor: '#e0e0e0',
                      borderRadius: 1,
                      px: 3,
                      bgcolor: 'white',
                    }}
                  >
                    Cancel
                  </Button>
                </Box>
              </Form>
            )}
          </Formik>
        </DialogContent>
      </Dialog>

      {/* Service Area Modal */}
      <Dialog
        open={isServiceAreaModalOpen}
        onClose={handleCloseServiceAreaModal}
        fullWidth
        maxWidth="md"
        PaperProps={{
          style: {
            backgroundColor: "white",
            borderRadius: "8px",
          },
        }}
      >
        <DialogTitle
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            padding: { xs: "16px", sm: "20px 24px" },
            borderBottom: "1px solid rgba(0, 0, 0, 0.12)",
            bgcolor: "white",
          }}
        >
          <Typography
            variant="h6"
            component="div"
            sx={{
              fontWeight: 500,
              color: "black",
              fontSize: { xs: "1.1rem", sm: "1.25rem" },
            }}
          >
            Service area
          </Typography>
          <IconButton
            edge="end"
            color="inherit"
            onClick={handleCloseServiceAreaModal}
            aria-label="close"
          >
            <CloseIcon />
          </IconButton>
        </DialogTitle>

        <DialogContent sx={{ px: { xs: 2, sm: 3 }, py: { xs: 2, sm: 2 }, bgcolor: "white" }}>
          <Box sx={{ mb: 3 }}>
            <Typography
              variant="body2"
              sx={{
                color: "rgba(0, 0, 0, 0.7)",
                fontSize: { xs: "0.875rem", sm: "0.9rem" },
                display: "inline",
              }}
            >
              Let customers know where your business provides deliveries or services.{" "}
            </Typography>
            {/* <Typography
              component="span"
              sx={{
                color: "#1976d2",
                cursor: "pointer",
                fontSize: { xs: "0.875rem", sm: "0.9rem" },
                "&:hover": {
                  textDecoration: "underline",
                },
              }}
            >
              Learn more
            </Typography> */}
          </Box>

          <Formik
            initialValues={{ serviceAreas }}
            validationSchema={serviceAreaValidationSchema}
            onSubmit={handleSaveServiceAreas}
          >
            {({ values, errors, touched, isValid, handleSubmit, setFieldValue }: FormikProps<{ serviceAreas: string[] }>) => (
              <Form onSubmit={handleSubmit}>
                <TextField
                  fullWidth
                  placeholder="Search area"
                  value={searchArea}
                  onChange={(e) => setSearchArea(e.target.value)}
                  sx={{
                    mb: 3,
                    '& .MuiOutlinedInput-root': {
                      borderRadius: 1,
                      '& fieldset': {
                        borderColor: 'rgba(0, 0, 0, 0.23)',
                      },
                    },
                  }}
                  InputProps={{
                    style: { color: "black" },
                    startAdornment: (
                      <InputAdornment position="start">
                        {/* <SearchIcon sx={{ color: 'rgba(0, 0, 0, 0.54)' }} /> */}
                      </InputAdornment>
                    ),
                  }}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter') {
                      e.preventDefault();
                      handleAddServiceArea(searchArea, setFieldValue, values);
                    }
                  }}
                />

                {touched.serviceAreas && errors.serviceAreas && (
                  <Typography color="error" variant="body2" sx={{ mb: 2 }}>
                    {errors.serviceAreas}
                  </Typography>
                )}

                <Typography
                  variant="body2"
                  sx={{
                    mb: 1.5,
                    color: "black",
                    fontWeight: 500,
                    fontSize: "0.9rem",
                  }}
                >
                  Selected service areas
                </Typography>

                <Box sx={{
                  display: 'flex',
                  flexWrap: 'wrap',
                  gap: 1,
                  mb: 3,
                  maxHeight: '300px',
                  overflowY: 'auto',
                  p: 1
                }}>
                  {values.serviceAreas.map((area, index) => (
                    <Chip
                      key={index}
                      label={area}
                      onDelete={() => handleRemoveServiceArea(area, setFieldValue, values)}
                      sx={{
                        bgcolor: '#f5f5f5',
                        color: 'rgba(0, 0, 0, 0.87)',
                        borderRadius: '16px',
                        '& .MuiChip-deleteIcon': {
                          color: 'rgba(0, 0, 0, 0.54)',
                        },
                      }}
                    />
                  ))}
                </Box>

                <Box sx={{
                  display: 'flex',
                  justifyContent: 'flex-start',
                  mt: 2,
                  gap: 2
                }}>
                  <Button
                    variant="contained"
                    type="submit"
                    sx={{
                      textTransform: 'none',
                      bgcolor: '#1976d2',
                      color: 'white',
                      '&:hover': {
                        bgcolor: '#1565c0',
                      }
                    }}
                  >
                    Save
                  </Button>
                  <Button
                    variant="outlined"
                    onClick={handleCloseServiceAreaModal}
                    sx={{
                      textTransform: 'none',
                      color: '#1976d2',
                      borderColor: '#e0e0e0',
                      bgcolor: 'white',
                    }}
                  >
                    Cancel
                  </Button>
                </Box>
              </Form>
            )}
          </Formik>
        </DialogContent>
      </Dialog>
    </Box>
  );
};

export default DemoScreen;
